import { ref, nextTick, onBeforeUnmount } from 'vue'
import { debounce } from 'lodash-es'

/**
 * 懒加载组合式函数
 */
export function useLazyLoad(options = {}) {
  const {
    threshold = 200,
    debounceDelay = 100
  } = options

  // ==================== 响应式数据 ====================
  const containerRef = ref(null)
  let scrollListener = null

  // ==================== 滚动监听 ====================
  /**
   * 设置滚动监听
   */
  const setupScrollListener = (callback) => {
    if (!containerRef.value || typeof callback !== 'function') return

    // 移除之前的监听器
    removeScrollListener()

    // 防抖处理滚动事件
    const debouncedCallback = debounce(() => {
      checkVisibleElements(callback)
    }, debounceDelay)

    scrollListener = debouncedCallback
    containerRef.value.addEventListener('scroll', scrollListener, { passive: true })

    // 初始检查
    nextTick(() => {
      checkVisibleElements(callback)
    })
  }

  /**
   * 移除滚动监听器
   */
  const removeScrollListener = () => {
    if (scrollListener && containerRef.value) {
      containerRef.value.removeEventListener('scroll', scrollListener)
      scrollListener = null
    }
  }

  /**
   * 检查可见元素
   */
  const checkVisibleElements = (callback) => {
    if (!containerRef.value) return

    const container = containerRef.value
    const containerRect = container.getBoundingClientRect()
    const containerTop = containerRect.top
    const containerBottom = containerRect.bottom

    // 获取所有需要检查的元素
    const elements = container.querySelectorAll('[data-category-id]')

    elements.forEach(element => {
      const elementRect = element.getBoundingClientRect()
      const elementId = element.dataset.categoryId

      if (!elementId) return

      // 检查元素是否在视口内或即将进入视口
      const isVisible = elementRect.top < containerBottom + threshold &&
                       elementRect.bottom > containerTop - threshold

      if (isVisible) {
        callback(elementId, element)
      }
    })
  }

  /**
   * 手动触发检查
   */
  const triggerCheck = (callback) => {
    if (typeof callback === 'function') {
      nextTick(() => {
        checkVisibleElements(callback)
      })
    }
  }

  // ==================== 生命周期 ====================
  onBeforeUnmount(() => {
    removeScrollListener()
  })

  return {
    containerRef,
    setupScrollListener,
    removeScrollListener,
    triggerCheck
  }
}

/**
 * 响应式布局组合式函数
 */
export function useResponsiveLayout(options = {}) {
  const {
    minItemWidth = 100,
    minItemsPerRow = 3,
    debounceDelay = 150
  } = options

  // ==================== 响应式数据 ====================
  const containerRef = ref(null)
  const itemsPerRow = ref(minItemsPerRow)
  let resizeTimer = null

  // ==================== 计算布局 ====================
  /**
   * 计算每行应该显示的项目数量
   */
  const calculateItemsPerRow = () => {
    if (!containerRef.value) return

    // 使用requestAnimationFrame优化性能
    requestAnimationFrame(() => {
      const containerWidth = containerRef.value.clientWidth
      const maxItemsPerRow = Math.floor(containerWidth / minItemWidth)
      itemsPerRow.value = Math.max(maxItemsPerRow, minItemsPerRow)
    })
  }

  /**
   * 防抖处理窗口大小变化
   */
  const handleResize = debounce(() => {
    calculateItemsPerRow()
  }, debounceDelay)

  /**
   * 设置响应式监听
   */
  const setupResponsiveListener = () => {
    calculateItemsPerRow()
    window.addEventListener('resize', handleResize)
  }

  /**
   * 移除响应式监听
   */
  const removeResponsiveListener = () => {
    window.removeEventListener('resize', handleResize)
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
  }

  // ==================== 生命周期 ====================
  onBeforeUnmount(() => {
    removeResponsiveListener()
  })

  return {
    containerRef,
    itemsPerRow,
    calculateItemsPerRow,
    setupResponsiveListener,
    removeResponsiveListener
  }
}
