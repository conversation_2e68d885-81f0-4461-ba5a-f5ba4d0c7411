<template>
  <div class="address-form">
    <div class="address-form__content">
      <WoFormItem label="收货人" v-model="addressForm.recName" placeholder="请填写收货人姓名" />
      <WoFormItem label="手机号" v-model="addressForm.recPhone" maxlength="11" placeholder="请填写手机号" />
      <WoFormItem label="所在地区" @click="onSelectRegion">
        <template #input>
          <div class="address-form__region-value"
            :class="{ 'address-form__region-value--placeholder': !addressForm.region }">
            {{ addressForm.region || '请选择地址' }}
          </div>
        </template>
        <template #rightIcon>
          <img src="@/static/images/arrow-right-black.png" alt="选择地区" class="address-form__arrow-icon"
            @click="onSelectRegion">
        </template>
      </WoFormItem>
      <WoFormItem label="详细地址" rows="1" type="textarea" autosize v-model="addressForm.addrDetail"
        placeholder="请填写详细地址" />
    </div>
    <div class="address-form__actions">
      <WoButton size="xlarge" type="primary" block :loading="isSubmitting" @click="saveAddress">
        保存
      </WoButton>
    </div>
  </div>
  <van-popup v-model:show="showCascader" round position="bottom" :lazy-render="false">
    <van-cascader class="address-form__cascader" v-model="cascaderValue" title="所选地区" :options="cascaderOptions"
      :field-names="cascaderFieldNames" @close="showCascader = false" @change="onCascaderChange"
      @finish="onCascaderFinish" />
  </van-popup>
</template>

<script setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce, cloneDeep } from 'lodash-es'
import WoFormItem from '@components/WoElementCom/WoFormItem.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { addAddr, editAddr, queryAddrArea, queryUserAddrList } from '@api/interface/address.js'
import base64 from '@utils/base64.js'

// 路由实例
const router = useRouter()
const route = useRoute()

// 状态管理
const isSubmitting = ref(false)
const isLoading = ref(false)

// 提交地址的表单数据
const addressForm = reactive({
  recName: '',    // 收货人姓名
  recPhone: '',   // 收货人手机号
  region: '',     // 地区文本描述
  addrDetail: '', // 详细地址
  areaId: '',     // 最后一级地区ID
  areaType: ''    // 最后一级地区类型
})

// 级联选择器相关数据
const showCascader = ref(false)         // 控制弹层显示状态
const cascaderValue = ref('')           // 当前选中的值 (单值模式)
const cascaderValueDetails = ref([])    // 当前选中的完整对象数组 (多级路径)
const cascaderOptions = ref([])         // 级联数据源

// 字段映射配置
const cascaderFieldNames = {
  text: 'areaName',     // 显示文本对应的字段名
  value: 'areaId',      // 值对应的字段名
  children: 'children'  // 子选项对应的字段名
}

// 计算属性
const isEditMode = computed(() => !!route.query.addrId)
const phoneRegex = /^1\d{10}$/

// 构建地址数组 - 将地址对象转换为级联选择器需要的数据格式
const buildAddressArray = (address) => {
  const addrArr = []

  // 按省市区街道顺序构建数组
  if (address.provinceName) {
    addrArr.push({
      areaId: address.provinceId,
      areaName: address.provinceName,
      areaType: '1'
    })
  }

  if (address.cityName) {
    addrArr.push({
      areaId: address.cityId,
      areaName: address.cityName,
      areaType: '2'
    })
  }

  if (address.countyName) {
    addrArr.push({
      areaId: address.countyId,
      areaName: address.countyName,
      areaType: '3'
    })
  }

  if (address.townName) {
    addrArr.push({
      areaId: address.townId,
      areaName: address.townName,
      areaType: '4'
    })
  }

  return addrArr
}

// 填充地址表单数据
const fillAddressForm = (address) => {
  // 填充基础表单数据
  addressForm.recName = address.recName || ''
  addressForm.recPhone = address.recPhone || ''
  addressForm.region = [address.provinceName, address.cityName, address.countyName, address.townName].filter(Boolean).join('/') || ''
  addressForm.addrDetail = address.addrDetail || ''
  addressForm.areaId = address.areaId || ''
  addressForm.areaType = address.areaType || ''

  // 构建级联选择器需要的地址数组
  const addrArr = buildAddressArray(address)
  cascaderValueDetails.value = addrArr

  // 设置当前选中值
  if (addrArr.length > 0) {
    const last = addrArr[addrArr.length - 1]
    cascaderValue.value = last.areaId
  }
}

// 查询已选择地址的详细信息，用于编辑模式下加载完整的地址层级数据
const querySelectedAddrInfo = async () => {
  // 不存在已选中数据，流程结束
  if (cascaderValueDetails.value.length === 0) return

  // 加载指定层级的地址数据
  const loadAddressLevel = async (index, parentObj) => {
    const current = cascaderValueDetails.value[index]
    if (!current) return null

    // 构建查询参数
    const area = `{"areaId":"${current.areaId}","areaType":"${current.areaType}"}`
    const [err, json] = await queryAddrArea(area)
    if (err || json.length === 0) return null

    // 第一级（省）特殊处理
    if (index === 0) {
      const obj = cascaderOptions.value.find(item => item.areaId === current.areaId)
      if (obj) {
        obj.children = json
        return obj
      }
      return null
    }

    // 其他级别，从父对象的children中查找
    if (parentObj && parentObj.children) {
      const obj = parentObj.children.find(item => item.areaId === current.areaId)
      if (obj) {
        obj.children = json
        return obj
      }
    }
    return null
  }

  // 依次加载省市区街道数据
  const provinceObj = await loadAddressLevel(0)
  if (!provinceObj) return

  const cityObj = await loadAddressLevel(1, provinceObj)
  if (!cityObj) return

  // 加载区县数据
  await loadAddressLevel(2, cityObj)
  // 街道级别不需要继续加载子级
}

// 选择地区按钮点击处理
const onSelectRegion = () => {
  showCascader.value = true
}

// 级联选择器选项变化处理 - 使用防抖优化
const onCascaderChange = debounce(async ({ selectedOptions, tabIndex }) => {
  const selected = selectedOptions[tabIndex]

  // 如果当前节点已经加载过子节点，则不需要再次请求
  if (selected.children && selected.children.length > 0) {
    return
  }

  // 构建查询参数
  const area = JSON.stringify({
    areaId: selected.areaId,
    areaType: selected.areaType
  })

  try {
    showLoadingToast()
    const [err, json] = await queryAddrArea(area)
    closeToast()

    if (err) {
      showToast(err.msg || '查询失败')
      return
    }

    // 如果没有下一级数据，说明当前就是最后一级，直接完成选择
    if (!json || json.length === 0) {
      // 标记为叶子节点
      selected.children = null
      // 更新级联选项（通过创建新引用触发响应式更新）
      cascaderOptions.value = [...cascaderOptions.value]
      // 直接完成选择
      onCascaderFinish(selectedOptions)
      return
    }

    // 使用lodash深拷贝优化性能
    const newOptions = cloneDeep(cascaderOptions.value)

    // 查找并更新选中路径上的节点
    let currentLevel = newOptions
    let targetNode = null

    // 遍历选中路径，找到需要更新的节点
    for (let i = 0; i <= tabIndex; i++) {
      const currentId = selectedOptions[i].areaId
      const foundIndex = currentLevel.findIndex(item => item.areaId === currentId)

      if (foundIndex === -1) break

      if (i === tabIndex) {
        // 找到目标节点
        targetNode = currentLevel[foundIndex]
      } else if (currentLevel[foundIndex].children) {
        // 继续向下查找
        currentLevel = currentLevel[foundIndex].children
      } else {
        break
      }
    }

    // 更新找到的节点
    if (targetNode) {
      targetNode.children = json.map(item => ({
        ...item,
        children: []
      }))

      // 更新级联选项
      cascaderOptions.value = newOptions
    }
  } catch (err) {
    console.error('查询下一级数据失败:', err)
    closeToast()
    showToast(err.message || err.msg || '查询失败')
  }
}, 300)

// 级联选择器完成选择处理
const onCascaderFinish = (selectedOptions) => {
  showCascader.value = false
  cascaderValueDetails.value = selectedOptions

  // 拼接选中的地址文本
  if (selectedOptions && selectedOptions.length > 0) {
    addressForm.region = selectedOptions.map(option => option.areaName).join('/')

    // 存储地址ID信息，可用于提交表单
    const lastOption = selectedOptions[selectedOptions.length - 1]
    addressForm.areaId = lastOption.areaId
    addressForm.areaType = lastOption.areaType
  }
}

// 表单验证函数
const validateForm = () => {
  if (!addressForm.recName.trim()) {
    showToast('请填写收货人姓名')
    return false
  }
  if (!addressForm.recPhone.trim()) {
    showToast('请填写手机号')
    return false
  }
  if (!phoneRegex.test(addressForm.recPhone)) {
    showToast('请填写正确的手机号')
    return false
  }
  if (!addressForm.region) {
    showToast('请选择所在地区')
    return false
  }
  if (!addressForm.addrDetail.trim()) {
    showToast('请填写详细地址')
    return false
  }
  return true
}

// 构建请求参数
const buildRequestParams = () => {
  const addr = cascaderValueDetails.value
  return {
    recName: base64(addressForm.recName.trim()),
    recPhone: addressForm.recPhone.trim(),
    addrDetail: addressForm.addrDetail.trim(),
    provinceId: addr[0]?.areaId || '',
    provinceName: addr[0]?.areaName || '',
    cityId: addr[1]?.areaId || '',
    cityName: addr[1]?.areaName || '',
    countyId: addr[2]?.areaId || '',
    countyName: addr[2]?.areaName || '',
    townId: addr[3]?.areaId || '',
    townName: addr[3]?.areaName || ''
  }
}

// 保存地址信息 - 使用防抖优化
const saveAddress = debounce(async () => {
  if (isSubmitting.value) return

  // 表单验证
  if (!validateForm()) return

  isSubmitting.value = true
  showLoadingToast()

  try {
    const baseParams = buildRequestParams()
    const addrId = route.query.addrId

    let result
    if (isEditMode.value) {
      // 编辑模式
      result = await editAddr({
        ...baseParams,
        addressId: addrId
      })
    } else {
      // 新增模式
      result = await addAddr(baseParams)
    }

    const [err] = result

    if (err) {
      showToast(err.msg || `${isEditMode.value ? '保存' : '新增'}失败`)
      return
    }

    showToast(`${isEditMode.value ? '保存' : '新增'}成功`)

    // 延迟返回，让用户看到成功提示
    setTimeout(() => {
      router.back()
    }, 1500)

  } catch (error) {
    console.error('保存地址失败:', error)
    showToast(error.message || `${isEditMode.value ? '保存' : '新增'}失败`)
  } finally {
    closeToast()
    isSubmitting.value = false
  }
}, 300)


onMounted(async () => {
  // 初始化级联数据源 - 加载省级数据
  const [err, json] = await queryAddrArea()
  if (err) {
    showToast(err.msg)
  } else {
    cascaderOptions.value = json.map(item => {
      // 初始化时，设置空数组表示需要加载子节点
      item.children = []
      return item
    })
  }

  // 判断是否有地址数据 - 编辑模式
  const addrId = route.query.addrId
  if (!addrId) return

  // 检查params中是否有地址数据 - 从列表页传递过来的完整地址对象
  if (route.params.address) {
    // 直接使用params中的地址数据
    fillAddressForm(route.params.address)
    // 加载地址层级数据
    await querySelectedAddrInfo()
  } else {
    // 通过接口获取地址列表，根据addrId匹配
    showLoadingToast('加载中...')
    const [listErr, addrList] = await queryUserAddrList()
    closeToast()

    if (!listErr && addrList && addrList.length > 0) {
      // 查找匹配的地址
      const matchedAddr = addrList.find(item => item.addressId === addrId)
      if (matchedAddr) {
        // 填充地址表单
        fillAddressForm(matchedAddr)
        // 加载地址层级数据
        await querySelectedAddrInfo()
      } else {
        showToast('未找到对应的地址信息')
      }
    } else {
      showToast(listErr?.msg || '获取地址信息失败')
    }
  }
})
</script>

<style scoped lang="less">
.address-form {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: @bg-color-white;

  &__content {
    flex: 1;
    padding: 0 17px;
  }

  &__region-value {
    flex: 1;
    font-size: @font-size-14;
    color: @text-color-primary;

    &--placeholder {
      color: @text-color-tertiary;
    }
  }

  &__actions {
    padding: 40px 17px 20px;
    margin-top: auto;
  }

  &__arrow-icon {
    width: 6px;
    height: 11px;
    margin-left: 5px;
  }

  &__cascader {
    :deep(.van-tabs__line) {
      background: @theme-color;
    }

    :deep(.van-cascader__option.van-cascader__option--selected) {
      color: @theme-color;
    }
  }
}
</style>
