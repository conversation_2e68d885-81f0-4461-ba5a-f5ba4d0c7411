import { ref, computed } from 'vue'

import { showLoadingToast, showToast, closeToast } from 'vant'
import { getClassification } from '@api/interface/goods.js'
import { getBizCode } from '@utils/curEnv.js'

/**
 * 分类数据管理组合式函数
 */
export function useCategory() {
  // ==================== 响应式数据 ====================
  const firstCategories = ref([])
  const secondCategories = ref([])
  const thirdCategories = ref(new Map())

  // 加载状态
  const isFirstCategoryLoading = ref(true)
  const isSecondCategoryLoading = ref(false)
  const loadingThirdCategories = ref(new Set())
  const loadedSecondCategoryIds = ref(new Set())

  // 当前选中的一级分类索引
  const activeFirstCategory = ref(0)

  // API缓存
  const apiCache = new Map()

  // 懒加载配置
  const INITIAL_LOAD_COUNT = 3
  const LOAD_THRESHOLD = 200

  // ==================== 计算属性 ====================
  const isInitialLoading = computed(() => isFirstCategoryLoading.value)

  const thirdCategoriesGroups = computed(() => {
    const groups = []

    secondCategories.value.forEach(secondCategory => {
      const items = thirdCategories.value.get(secondCategory.id) || []
      const isLoading = loadingThirdCategories.value.has(secondCategory.id)
      const isLoaded = loadedSecondCategoryIds.value.has(secondCategory.id)

      groups.push({
        id: secondCategory.id,
        title: secondCategory.name,
        items: items,
        isLoading: isLoading,
        isLoaded: isLoaded,
        isEmpty: isLoaded && items.length === 0
      })
    })

    return groups
  })

  // ==================== 工具函数 ====================
  const getCacheKey = (params) => JSON.stringify(params)

  // ==================== API 调用 ====================
  /**
   * 获取分类数据
   */
  const fetchCategories = async (id = '') => {
    const cacheKey = getCacheKey({ category_pid: id, page_no: 1, page_size: 500 })

    try {
      // 设置加载状态
      if (id === '') {
        isFirstCategoryLoading.value = true
      } else if (!isFirstCategoryLoading.value) {
        isSecondCategoryLoading.value = true
        showLoadingToast()
      }

      // 检查缓存
      if (apiCache.has(cacheKey)) {
        const cachedData = apiCache.get(cacheKey)
        await processCategories(cachedData, id)
        return
      }

      // 调用API
      const [err, data] = await getClassification({
        bizCode: getBizCode('GOODS'),
        category_pid: id,
        page_no: 1,
        page_size: 500
      })

      if (err) {
        showToast('获取分类数据失败')
        return
      }

      if (data && Array.isArray(data)) {
        // 缓存数据（5分钟过期）
        apiCache.set(cacheKey, data)
        setTimeout(() => apiCache.delete(cacheKey), 5 * 60 * 1000)

        await processCategories(data, id)
      }
    } catch (error) {
      console.error('获取分类数据出错:', error)
      showToast('获取分类数据失败')
    } finally {
      // 关闭加载状态
      if (id === '') {
        isFirstCategoryLoading.value = false
      } else {
        isSecondCategoryLoading.value = false
      }
      closeToast()
    }
  }

  /**
   * 获取单个二级分类的三级分类
   */
  const fetchThirdCategories = async (secondCategoryId) => {
    if (!secondCategoryId) return []

    const cacheKey = getCacheKey({ category_pid: secondCategoryId, page_no: 1, page_size: 500 })

    // 检查缓存
    if (apiCache.has(cacheKey)) {
      const cachedData = apiCache.get(cacheKey)
      return cachedData.filter(item => item.depth === 3)
    }

    try {
      const [err, data] = await getClassification({
        bizCode: getBizCode('GOODS'),
        category_pid: secondCategoryId,
        page_no: 1,
        page_size: 500
      })

      if (err || !data) return []

      // 缓存数据
      apiCache.set(cacheKey, data)
      setTimeout(() => apiCache.delete(cacheKey), 5 * 60 * 1000)

      return data.filter(item => item.depth === 3)
    } catch (error) {
      console.error('获取三级分类出错:', error)
      return []
    }
  }

  // ==================== 数据处理 ====================
  /**
   * 处理分类数据
   */
  const processCategories = async (data, id) => {
    if (id === '') {
      // 处理一级分类
      firstCategories.value = data.filter(item => item.depth === 1)
    } else {
      // 处理二级分类
      const secondLevel = data.filter(item => item.depth === 2)
      secondCategories.value = secondLevel

      // 清空之前的三级分类数据
      thirdCategories.value.clear()
      loadedSecondCategoryIds.value.clear()
      loadingThirdCategories.value.clear()

      // 加载初始的三级分类
      if (secondLevel.length > 0) {
        await loadInitialThirdCategories(secondLevel)
      }
    }
  }

  /**
   * 加载初始的几个二级分类的三级分类
   */
  const loadInitialThirdCategories = async (secondLevel) => {
    const initialCategories = secondLevel.slice(0, INITIAL_LOAD_COUNT)

    const loadPromises = initialCategories.map(async (secondCategory) => {
      loadingThirdCategories.value.add(secondCategory.id)

      try {
        const thirdItems = await fetchThirdCategories(secondCategory.id)
        thirdCategories.value.set(secondCategory.id, thirdItems)
        loadedSecondCategoryIds.value.add(secondCategory.id)
      } catch (error) {
        console.error(`加载二级分类 ${secondCategory.id} 的三级分类失败:`, error)
        thirdCategories.value.set(secondCategory.id, [])
        loadedSecondCategoryIds.value.add(secondCategory.id)
      } finally {
        loadingThirdCategories.value.delete(secondCategory.id)
      }
    })

    await Promise.all(loadPromises)
  }

  /**
   * 懒加载单个二级分类的三级分类
   */
  const loadThirdCategoryLazy = async (secondCategoryId) => {
    if (loadedSecondCategoryIds.value.has(secondCategoryId) ||
        loadingThirdCategories.value.has(secondCategoryId)) {
      return
    }

    loadingThirdCategories.value.add(secondCategoryId)

    try {
      const thirdItems = await fetchThirdCategories(secondCategoryId)
      thirdCategories.value.set(secondCategoryId, thirdItems)
      loadedSecondCategoryIds.value.add(secondCategoryId)
    } catch (error) {
      console.error(`懒加载二级分类 ${secondCategoryId} 的三级分类失败:`, error)
      thirdCategories.value.set(secondCategoryId, [])
      loadedSecondCategoryIds.value.add(secondCategoryId)
    } finally {
      loadingThirdCategories.value.delete(secondCategoryId)
    }
  }

  // ==================== 分类操作 ====================
  /**
   * 根据分类ID查找并设置当前选中的一级分类
   */
  const findAndSetActiveCategoryById = (categoryId) => {
    if (!categoryId || !firstCategories.value.length) return false

    const index = firstCategories.value.findIndex(category => category.id === categoryId)
    if (index !== -1) {
      activeFirstCategory.value = index
      return true
    }
    return false
  }

  /**
   * 获取二级和三级分类
   */
  const fetchSecondAndThirdCategories = async (firstCategoryId) => {
    if (!firstCategoryId) return
    await fetchCategories(firstCategoryId)
  }

  return {
    // 响应式数据
    firstCategories,
    secondCategories,
    thirdCategories,
    activeFirstCategory,
    isFirstCategoryLoading,
    isSecondCategoryLoading,
    loadingThirdCategories,
    loadedSecondCategoryIds,

    // 计算属性
    isInitialLoading,
    thirdCategoriesGroups,

    // 方法
    fetchCategories,
    fetchThirdCategories,
    loadThirdCategoryLazy,
    findAndSetActiveCategoryById,
    fetchSecondAndThirdCategories,

    // 常量
    LOAD_THRESHOLD
  }
}
