<template>
  <article class="address-card" :class="{ 'address-card--default': isDefault }" @click="handleClick">
    <header class="address-card__header">
      <div class="contact-info">
        <h3 class="contact-info__name">{{ address.recName }}</h3>
        <span class="contact-info__phone">{{ address.recPhone }}</span>
      </div>
    </header>

    <div class="address-card__content">
      <p class="address-detail">{{ fullAddress }}</p>
    </div>

    <footer class="address-card__actions">
      <div v-if="isDefault" class="default-badge">
        <img src="@/static/images/wo-select.png" class="default-badge__icon" alt="默认地址" loading="lazy" width="15"
          height="15">
        <span class="default-badge__text">已默认</span>
      </div>

      <div class="action-buttons">
        <button v-if="!isDefault" type="button" class="action-btn action-btn--edit" @click.stop="handleEdit"
          aria-label="编辑地址">
          编辑
        </button>
        <button type="button" class="action-btn action-btn--delete" @click.stop="handleDelete" aria-label="删除地址">
          删除
        </button>
      </div>
    </footer>
  </article>
</template>

<script setup>
import { defineEmits, defineProps, computed, toRefs } from 'vue'
import { compact, join, throttle } from 'lodash-es'

const props = defineProps({
  address: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['click', 'edit', 'delete'])

// 解构 props，获得响应式引用
const { address } = toRefs(props)

// 计算属性优化性能，避免模板中重复计算
const isDefault = computed(() => address.value.isDefault === '1')

const fullAddress = computed(() => {
  const { provinceName, cityName, countyName, townName, addrDetail } = address.value
  const regions = compact([provinceName, cityName, countyName, townName])
  return `${join(regions, '')} ${addrDetail || ''}`
})

// 使用节流优化点击事件
const handleClick = throttle(() => {
  emit('click', address.value)
}, 300)

const handleEdit = throttle(() => {
  emit('edit', address.value)
}, 300)

const handleDelete = throttle(() => {
  emit('delete', address.value)
}, 300)
</script>

<style scoped lang="less">
.address-card {
  padding: 15px;
  background-color: @bg-color-white;
  border-radius: @radius-4;
  margin-bottom: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &--default {
    border: 1px solid @color-orange;
  }
}

.address-card__header {
  margin-bottom: 11px;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 10px;

  &__name {
    font-size: @font-size-16;
    font-weight: @font-weight-600;
    color: @text-color-primary;
    margin: 0;
  }

  &__phone {
    font-size: @font-size-16;
    color: @text-color-primary;
    font-weight: @font-weight-600;
  }
}

.address-card__content {
  margin-bottom: 16px;
}

.address-detail {
  font-size: @font-size-13;
  color: @text-color-primary;
  line-height: 17px;
  margin: 0;
  .multi-ellipsis(2);
}

.address-card__actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.default-badge {
  display: flex;
  align-items: center;
  color: #F9842E;
  font-size: @font-size-12;
  font-weight: @font-weight-500;

  &__icon {
    width: 15px;
    height: 15px;
    margin-right: 4px;
  }

  &__text {
    color: #F9842E;
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 25px;
  background: #F6F6F6;
  border: none;
  border-radius: @radius-4;
  font-size: @font-size-12;
  color: @text-color-primary;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #E6E6E6;
  }

  &:active {
    transform: scale(0.95);
  }

  &--edit {
    // 编辑按钮特定样式
  }

  &--delete {
    // 删除按钮特定样式
  }
}
</style>
